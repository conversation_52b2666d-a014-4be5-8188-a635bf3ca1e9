<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI Classification - Data Assortment All in One</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .program-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .form-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            display: inline-block;
        }

        .status-ready { background-color: #28a745; }
        .status-processing { background-color: #ffc107; animation: pulse 1.5s infinite; }
        .status-error { background-color: #dc3545; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .model-selector {
            display: flex;
            gap: 0.5rem;
        }

        .model-option {
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            flex: 1;
            font-weight: 500;
        }

        .model-option:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .model-option.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }

        .progress-container {
            background: #e9ecef;
            border-radius: 0.5rem;
            height: 1rem;
            margin: 1rem 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 0.5rem;
        }

        .log-container {
            background: #f8f9fa;
            color: #495057;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            padding: 1rem;
            border-radius: 0.375rem;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .stat-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
        }

        .auth-section {
            text-align: center;
            padding: 2rem;
        }

        .auth-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .auth-status.authenticated {
            background: rgba(25, 135, 84, 0.1);
            color: #198754;
            border: 2px solid #198754;
        }

        .auth-status.not-authenticated {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 2px solid #dc3545;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .alert-custom {
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border: none;
        }

        .alert-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-left: 4px solid #dc3545;
        }

        .alert-success {
            background: rgba(25, 135, 84, 0.1);
            color: #198754;
            border-left: 4px solid #198754;
        }
    </style>
</head>
<body>
    <div class="program-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-robot"></i> AI Classification</h1>
                    <p class="mb-0">Phân loại sản phẩm thông minh bằng AI với OpenAI GPT</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="status-indicator status-ready" id="statusIndicator"></span>
                        <span id="statusText">Sẵn sàng</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Google Sheet Information Section -->
        <div class="form-section">
            <h3 class="section-title"><i class="fas fa-table"></i> Google Sheet Information</h3>

            <div class="row mb-3">
                <div class="col-md-8">
                    <label class="form-label"><i class="fas fa-link"></i> Spreadsheet URL:</label>
                    <input type="text" id="sheetUrl" class="form-control"
                           placeholder="Nhập Google Spreadsheet URL">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button id="loadSheetBtn" class="btn btn-gradient w-100">
                        <i class="fas fa-download"></i> Load Sheet
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-file-alt"></i> Brand Sheet:</label>
                    <select id="brandSheetSelect" class="form-control" disabled>
                        <option value="">Chọn sheet Brand...</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-file-alt"></i> Deal Sheet:</label>
                    <select id="dealSheetSelect" class="form-control" disabled>
                        <option value="">Chọn sheet Deal list...</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button id="mapColumnsBtn" class="btn btn-outline-primary w-100" disabled>
                        <i class="fas fa-cog"></i> Map cột
                    </button>
                </div>
            </div>
        </div>

        <!-- AI Configuration Section -->
        <div class="form-section">
            <h3 class="section-title"><i class="fas fa-robot"></i> Cấu hình AI</h3>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label"><i class="fas fa-brain"></i> AI Model:</label>
                    <div class="model-selector">
                        <div class="model-option active" data-model="gpt-4o-mini">GPT-4o Mini</div>
                        <div class="model-option" data-model="gpt-3.5-turbo">GPT-3.5 Turbo</div>
                        <div class="model-option" data-model="gpt-4">GPT-4</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><i class="fas fa-list-ol"></i> Số dòng xử lý:</label>
                    <input type="number" id="lastRow" class="form-control"
                           value="100" min="1" max="10000">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button id="processBtn" class="btn btn-success w-100">
                        <i class="fas fa-play"></i> Bắt đầu phân loại
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="form-section">
            <h3 class="section-title"><i class="fas fa-chart-bar"></i> Thống kê</h3>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalProducts">0</div>
                    <div class="stat-label">Tổng sản phẩm</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalBrands">0</div>
                    <div class="stat-label">Tổng thương hiệu</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="apiCalls">0</div>
                    <div class="stat-label">API Calls</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="tokensUsed">0</div>
                    <div class="stat-label">Tokens sử dụng</div>
                </div>
            </div>

            <div class="progress-container">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Log Section -->
        <div class="form-section">
            <h3 class="section-title"><i class="fas fa-terminal"></i> Log xử lý</h3>
            <div id="logArea" class="log-container"></div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <button id="clearLogBtn" class="btn btn-outline-secondary">
                        <i class="fas fa-trash"></i> Xóa log
                    </button>
                </div>
                <div class="col-md-6 text-end">
                    <button id="exportResultsBtn" class="btn btn-primary" disabled>
                        <i class="fas fa-download"></i> Xuất kết quả
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Column Mapping Modal -->
    <div class="modal fade" id="columnMappingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-cog"></i> Cấu hình mapping cột</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Brand Sheet Mapping</h6>
                            <div class="mb-3">
                                <label class="form-label">Brand Code:</label>
                                <input type="text" id="brandCodeCol" class="form-control" value="A" placeholder="A">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Brand Type:</label>
                                <input type="text" id="brandTypeCol" class="form-control" value="E" placeholder="E">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Brand Name:</label>
                                <input type="text" id="brandNameCol" class="form-control" value="D" placeholder="D">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Deal Sheet Mapping</h6>
                            <div class="mb-3">
                                <label class="form-label">Deal Brand Code:</label>
                                <input type="text" id="dealBrandCodeCol" class="form-control" value="A" placeholder="A">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Deal Product:</label>
                                <input type="text" id="dealProductCol" class="form-control" value="H" placeholder="H">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Deal Price:</label>
                                <input type="text" id="dealPriceCol" class="form-control" value="AB" placeholder="AB">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Deal Pick:</label>
                                <input type="text" id="dealPickCol" class="form-control" value="N" placeholder="N">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="saveColumnMapping()">
                        <i class="fas fa-save"></i> Lưu mapping
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/ai_classification.js') }}"></script>
</body>
</html>
