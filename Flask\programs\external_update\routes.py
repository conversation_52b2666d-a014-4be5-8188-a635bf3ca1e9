"""
Flask routes cho External Update program
"""

from flask import Blueprint, render_template, request, jsonify, session
import json
import threading
import time
from datetime import datetime

from .core.external_logic import (
    ExternalUpdateProcessor, get_worksheets_list,
    validate_spreadsheet_access,
    SOURCE_COLUMN_MAPPING, TARGET_COLUMN_MAPPING,
    PROCESS_CONDITIONS, COLUMN_GROUPS,
    save_config, load_config
)
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from gsheet_manager import get_global_manager, extract_spreadsheet_id

external_update_bp = Blueprint('external_update', __name__,
                              template_folder='templates',
                              static_folder='static')

# Global variables để theo dõi tiến trình xử lý
processing_status = {
    'is_processing': False,
    'progress': 0,
    'logs': [],
    'result': None,
    'error': None
}

processing_lock = threading.Lock()

def get_manager():
    """Lấy GlobalGoogleSheetManager instance"""
    return get_global_manager()

@external_update_bp.route('/')
def index():
    """Trang chính của External Update"""
    return render_template('external_update/index.html')

# OAuth routes đã được chuyển sang global routes trong app.py
# Sử dụng /api/global/check_auth, /api/global/get_auth_url, /api/global/complete_auth

@external_update_bp.route('/api/validate_spreadsheet', methods=['POST'])
def validate_spreadsheet():
    """Kiểm tra quyền truy cập spreadsheet"""
    try:
        data = request.get_json()
        spreadsheet_url = data.get('spreadsheet_url', '').strip()

        if not spreadsheet_url:
            return jsonify({'success': False, 'error': 'URL không được để trống'})

        # Trích xuất spreadsheet ID
        spreadsheet_id = extract_spreadsheet_id(spreadsheet_url)
        if not spreadsheet_id:
            return jsonify({'success': False, 'error': 'URL không hợp lệ'})

        # Kiểm tra quyền truy cập
        manager = get_manager()
        result = validate_spreadsheet_access(spreadsheet_id, manager)

        if result['success']:
            return jsonify({
                'success': True,
                'spreadsheet_id': spreadsheet_id,
                'title': result['title']
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Không thể truy cập spreadsheet: {result["error"]}'
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@external_update_bp.route('/api/get_worksheets', methods=['POST'])
def get_worksheets():
    """Lấy danh sách worksheets từ spreadsheet"""
    try:
        data = request.get_json()
        spreadsheet_url = data.get('spreadsheet_url', '').strip()

        if not spreadsheet_url:
            return jsonify({'success': False, 'error': 'URL không được để trống'})

        # Trích xuất spreadsheet ID
        spreadsheet_id = extract_spreadsheet_id(spreadsheet_url)
        if not spreadsheet_id:
            return jsonify({'success': False, 'error': 'URL không hợp lệ'})

        # Lấy danh sách worksheets
        manager = get_manager()
        worksheets = get_worksheets_list(spreadsheet_id, manager)

        if 'error' in worksheets:
            return jsonify({'success': False, 'error': worksheets['error']})

        return jsonify({'success': True, 'worksheets': worksheets})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@external_update_bp.route('/api/get_column_mappings', methods=['GET'])
def get_column_mappings():
    """Lấy cấu hình ánh xạ cột"""
    try:
        return jsonify({
            'success': True,
            'source_mapping': SOURCE_COLUMN_MAPPING,
            'target_mapping': TARGET_COLUMN_MAPPING
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@external_update_bp.route('/api/get_process_conditions', methods=['GET'])
def get_process_conditions():
    """Lấy cấu hình điều kiện xử lý"""
    try:
        # Load từ file nếu có
        config = load_config()
        if config and 'process_conditions' in config:
            process_conditions = config['process_conditions']
        else:
            process_conditions = PROCESS_CONDITIONS

        return jsonify({
            'success': True,
            'process_conditions': process_conditions,
            'column_groups': COLUMN_GROUPS
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@external_update_bp.route('/api/save_process_conditions', methods=['POST'])
def save_process_conditions():
    """Lưu cấu hình điều kiện xử lý"""
    try:
        data = request.get_json()
        process_conditions = data.get('process_conditions', {})
        column_groups = data.get('column_groups', COLUMN_GROUPS)

        # Validate dữ liệu
        if not isinstance(process_conditions, dict):
            return jsonify({'success': False, 'error': 'Dữ liệu điều kiện xử lý không hợp lệ'})

        # Lưu vào file
        success = save_config(process_conditions, column_groups)

        if success:
            return jsonify({'success': True, 'message': 'Đã lưu cấu hình thành công'})
        else:
            return jsonify({'success': False, 'error': 'Không thể lưu cấu hình vào file'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@external_update_bp.route('/api/start_processing', methods=['POST'])
def start_processing():
    """Bắt đầu xử lý dữ liệu"""
    global processing_status

    with processing_lock:
        if processing_status['is_processing']:
            return jsonify({'success': False, 'error': 'Đang có tiến trình xử lý khác đang chạy'})

        # Reset trạng thái
        processing_status = {
            'is_processing': True,
            'progress': 0,
            'logs': [],
            'result': None,
            'error': None
        }

    try:
        data = request.get_json()

        # Lấy thông tin từ request
        source_url = data.get('source_url', '').strip()
        target_url = data.get('target_url', '').strip()
        source_sheet = data.get('source_sheet', '').strip()
        target_sheet = data.get('target_sheet', '').strip()
        start_row = data.get('start_row')
        header_row = data.get('header_row', 3)

        # Validate dữ liệu đầu vào
        if not all([source_url, target_url, source_sheet, target_sheet]):
            processing_status['is_processing'] = False
            return jsonify({'success': False, 'error': 'Thiếu thông tin bắt buộc'})

        # Chuyển đổi start_row
        if start_row:
            try:
                start_row = int(start_row)
            except ValueError:
                start_row = None

        # Chuyển đổi header_row
        try:
            header_row = int(header_row)
        except ValueError:
            header_row = 3

        # Bắt đầu xử lý trong thread riêng
        def process_data_thread():
            try:
                processor = ExternalUpdateProcessor()

                def log_callback(message, level="INFO"):
                    with processing_lock:
                        processing_status['logs'].append({
                            'timestamp': datetime.now().strftime('%H:%M:%S'),
                            'level': level,
                            'message': message
                        })

                def progress_callback(progress):
                    with processing_lock:
                        processing_status['progress'] = progress

                # Xử lý dữ liệu
                result = processor.process_data(
                    source_link=source_url,
                    target_link=target_url,
                    source_sheet=source_sheet,
                    target_sheet=target_sheet,
                    start_row=start_row,
                    header_row=header_row,
                    log_callback=log_callback,
                    progress_callback=progress_callback
                )

                with processing_lock:
                    processing_status['result'] = result
                    processing_status['progress'] = 100
                    processing_status['is_processing'] = False

            except Exception as e:
                with processing_lock:
                    processing_status['error'] = str(e)
                    processing_status['is_processing'] = False

        # Bắt đầu thread
        thread = threading.Thread(target=process_data_thread)
        thread.daemon = True
        thread.start()

        return jsonify({'success': True, 'message': 'Đã bắt đầu xử lý dữ liệu'})

    except Exception as e:
        processing_status['is_processing'] = False
        return jsonify({'success': False, 'error': str(e)})

@external_update_bp.route('/api/get_processing_status', methods=['GET'])
def get_processing_status():
    """Lấy trạng thái xử lý hiện tại"""
    with processing_lock:
        return jsonify({
            'success': True,
            'status': processing_status.copy()
        })

@external_update_bp.route('/api/stop_processing', methods=['POST'])
def stop_processing():
    """Dừng tiến trình xử lý"""
    global processing_status

    with processing_lock:
        if processing_status['is_processing']:
            processing_status['is_processing'] = False
            processing_status['logs'].append({
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'level': 'WARNING',
                'message': 'Tiến trình đã được dừng bởi người dùng'
            })
            return jsonify({'success': True, 'message': 'Đã dừng tiến trình xử lý'})
        else:
            return jsonify({'success': False, 'error': 'Không có tiến trình nào đang chạy'})

@external_update_bp.route('/api/clear_logs', methods=['POST'])
def clear_logs():
    """Xóa logs"""
    global processing_status

    with processing_lock:
        processing_status['logs'] = []
        return jsonify({'success': True, 'message': 'Đã xóa logs'})
