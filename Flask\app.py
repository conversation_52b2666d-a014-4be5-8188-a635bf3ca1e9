from flask import Flask, render_template, jsonify, request, session, redirect, url_for
import os
import importlib
import sys
from pathlib import Path
from gsheet_manager import get_global_manager

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # <PERSON><PERSON><PERSON> thành một secret key an toàn hơn

# Đăng ký các program module
def register_programs():
    programs_dir = Path(__file__).parent / 'programs'
    print(f"🔍 Looking for programs in: {programs_dir}")
    if not programs_dir.exists():
        print("❌ Programs directory does not exist")
        return

    app.programs = {}
    for item in programs_dir.iterdir():
        print(f"📁 Found item: {item.name}")
        if item.is_dir() and (item / '__init__.py').exists():
            program_name = item.name
            print(f"🔄 Loading program: {program_name}")
            try:
                module_path = f"programs.{program_name}"
                module = importlib.import_module(module_path)
                print(f"✅ Successfully imported {module_path}")

                # <PERSON><PERSON><PERSON> tra các thu<PERSON><PERSON> t<PERSON><PERSON> cần thiết
                name = getattr(module, 'NAME', program_name)
                description = getattr(module, 'DESCRIPTION', '')
                icon = getattr(module, 'ICON', '📦')

                print(f"📋 Program info: {name} - {description} - {icon}")

                if hasattr(module, 'setup'):
                    module.setup(app)
                    print(f"✅ Setup function called for {program_name}")
                else:
                    print(f"⚠️ No setup function found for {program_name}")

                app.programs[program_name] = {
                    'name': name,
                    'description': description,
                    'icon': icon,
                    'active': False
                }
                print(f"✅ Program {program_name} registered successfully")
            except Exception as e:
                print(f"❌ Không thể nạp chương trình {program_name}: {str(e)}")
                import traceback
                traceback.print_exc()
                # Vẫn thêm program với thông tin cơ bản để debug
                app.programs[program_name] = {
                    'name': program_name,
                    'description': f'Error loading: {str(e)}',
                    'icon': '❌',
                    'active': False
                }

# Gọi register_programs khi khởi tạo app
register_programs()

@app.route('/')
def index():
    return render_template('index.html', programs=getattr(app, 'programs', {}))

@app.route('/program/<program_id>')
def program_ui(program_id):
    print(f"🔄 Opening program: {program_id}")

    if not hasattr(app, 'programs') or program_id not in app.programs:
        print(f"❌ Program {program_id} not found")
        return "Chương trình không tồn tại", 404

    app.programs[program_id]['active'] = True

    # Đảm bảo session được khởi tạo
    if 'active_programs' not in session:
        session['active_programs'] = []

    if program_id not in session['active_programs']:
        session['active_programs'].append(program_id)
        print(f"✅ Added {program_id} to active programs")

    # Đảm bảo session được lưu
    session.modified = True

    print(f"📋 Current active programs: {session.get('active_programs', [])}")

    # Trả về nội dung UI của chương trình
    # External Update sử dụng blueprint riêng
    if program_id == 'external_update':
        from flask import redirect, url_for
        return redirect(url_for('external_update.index'))

    return render_template(f'programs/{program_id}.html')

@app.route('/api/programs')
def get_programs():
    if not hasattr(app, 'programs'):
        print("❌ No programs attribute found")
        return jsonify({})

    active_programs = session.get('active_programs', [])

    # Kiểm tra nếu tất cả chương trình đều active (có thể do lỗi session)
    # Nếu vậy, reset session để người dùng có thể sử dụng lại
    if len(active_programs) >= len(app.programs) and len(app.programs) > 0:
        print("⚠️ All programs are marked as active, resetting session...")
        session.clear()
        active_programs = []

    print(f"📋 Active programs in session: {active_programs}")
    print(f"📦 All programs: {list(app.programs.keys())}")

    available_programs = {
        k: v for k, v in app.programs.items()
        if k not in active_programs
    }

    print(f"✅ Available programs: {list(available_programs.keys())}")
    return jsonify(available_programs)

@app.route('/api/close_program', methods=['POST'])
def close_program():
    program_id = request.json.get('program_id')
    print(f"🔄 Closing program: {program_id}")

    if hasattr(app, 'programs') and program_id in app.programs:
        app.programs[program_id]['active'] = False

        # Đảm bảo session được khởi tạo
        if 'active_programs' not in session:
            session['active_programs'] = []

        # Xóa program khỏi danh sách active
        if program_id in session['active_programs']:
            session['active_programs'].remove(program_id)
            print(f"✅ Removed {program_id} from active programs")

        # Đảm bảo session được lưu
        session.modified = True

        print(f"📋 Current active programs: {session.get('active_programs', [])}")

        # Trả về thông tin program để frontend có thể sử dụng
        program_info = app.programs.get(program_id, {})
        return jsonify({
            "success": True,
            "program": program_info,
            "description": program_info.get('description', ''),
            "icon": program_info.get('icon', '')
        })

    return jsonify({"success": True})

# Global OAuth routes
@app.route('/api/global/check_auth', methods=['GET'])
def global_check_auth():
    """Kiểm tra trạng thái xác thực global"""
    try:
        manager = get_global_manager()
        if manager.is_authenticated():
            return jsonify({'success': True, 'authenticated': True})
        else:
            return jsonify({'success': True, 'authenticated': False})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/global/get_auth_url', methods=['GET'])
def global_get_auth_url():
    """Lấy URL xác thực OAuth global"""
    try:
        manager = get_global_manager()
        auth_url = manager.get_auth_url()
        return jsonify({'success': True, 'auth_url': auth_url})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/global/complete_auth', methods=['POST'])
def global_complete_auth():
    """Hoàn thành xác thực OAuth global với authorization code"""
    try:
        data = request.get_json()
        auth_code = data.get('auth_code', '').strip()

        if not auth_code:
            return jsonify({'success': False, 'error': 'Authorization code không được để trống'})

        manager = get_global_manager()
        success = manager.complete_oauth_flow(auth_code)

        if success:
            return jsonify({'success': True, 'message': 'Xác thực thành công'})
        else:
            return jsonify({'success': False, 'error': 'Xác thực thất bại'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/oauth/callback')
def oauth_callback():
    """OAuth callback route để nhận authorization code từ Google"""
    try:
        # Lấy authorization code từ URL parameters
        auth_code = request.args.get('code')
        error = request.args.get('error')

        if error:
            return f"""
            <html>
            <head><title>OAuth Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
                <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h2 style="color: #d32f2f;">❌ OAuth Error</h2>
                    <p>Có lỗi xảy ra trong quá trình xác thực: <strong>{error}</strong></p>
                    <p>Vui lòng đóng tab này và thử lại.</p>
                </div>
            </body>
            </html>
            """

        if not auth_code:
            return """
            <html>
            <head><title>OAuth Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
                <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h2 style="color: #d32f2f;">❌ Missing Authorization Code</h2>
                    <p>Không nhận được authorization code từ Google.</p>
                    <p>Vui lòng đóng tab này và thử lại.</p>
                </div>
            </body>
            </html>
            """

        # Tự động gửi authorization code về parent window và đóng popup
        return f"""
        <html>
        <head>
            <title>OAuth Success</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    padding: 40px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }}
                .container {{
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(10px);
                    border-radius: 20px;
                    padding: 40px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    text-align: center;
                    max-width: 500px;
                    width: 100%;
                }}
                .success-icon {{
                    font-size: 4rem;
                    color: #27ae60;
                    margin-bottom: 20px;
                    animation: bounce 1s ease-in-out;
                }}
                .title {{
                    color: #2c3e50;
                    font-size: 1.8rem;
                    font-weight: 600;
                    margin-bottom: 15px;
                }}
                .message {{
                    color: #34495e;
                    font-size: 1.1rem;
                    margin-bottom: 30px;
                    line-height: 1.6;
                }}
                .loading {{
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #3498db;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-right: 10px;
                }}
                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}
                @keyframes bounce {{
                    0%, 20%, 50%, 80%, 100% {{ transform: translateY(0); }}
                    40% {{ transform: translateY(-10px); }}
                    60% {{ transform: translateY(-5px); }}
                }}
                .auto-close {{
                    color: #7f8c8d;
                    font-size: 0.9rem;
                    margin-top: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="success-icon">✅</div>
                <h2 class="title">Xác thực thành công!</h2>
                <p class="message">
                    <span class="loading"></span>
                    Đang tự động hoàn thành xác thực...
                </p>
                <p class="auto-close">Cửa sổ này sẽ tự động đóng sau vài giây.</p>
            </div>

            <script>
                // Tự động gửi authorization code về parent window
                window.onload = function() {{
                    try {{
                        // Gửi authorization code về parent window
                        if (window.opener) {{
                            window.opener.postMessage({{
                                type: 'OAUTH_SUCCESS',
                                authCode: '{auth_code}'
                            }}, '*');

                            // Đóng popup sau 2 giây
                            setTimeout(() => {{
                                window.close();
                            }}, 2000);
                        }} else {{
                            // Fallback: redirect về main app với code
                            setTimeout(() => {{
                                window.location.href = '/?auth_code={auth_code}';
                            }}, 2000);
                        }}
                    }} catch (error) {{
                        console.error('Error in OAuth callback:', error);
                        // Fallback: redirect về main app
                        setTimeout(() => {{
                            window.location.href = '/';
                        }}, 3000);
                    }}
                }};
            </script>
        </body>
        </html>
        """

    except Exception as e:
        return f"""
        <html>
        <head><title>OAuth Error</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
            <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #d32f2f;">❌ Server Error</h2>
                <p>Có lỗi xảy ra: <strong>{str(e)}</strong></p>
                <p>Vui lòng đóng tab này và thử lại.</p>
            </div>
        </body>
        </html>
        """

@app.route('/api/reset_session')
def reset_session():
    """Reset session để debug - xóa tất cả active programs"""
    session.clear()
    print("🔄 Session cleared - all programs are now available")
    return jsonify({"success": True, "message": "Session reset successfully"})

if __name__ == '__main__':
    print("🚀 Starting Flask app...")
    print("📁 Programs loaded:", getattr(app, 'programs', {}))
    print("🌐 Server will start at: http://localhost:5000")
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        import traceback
        traceback.print_exc()