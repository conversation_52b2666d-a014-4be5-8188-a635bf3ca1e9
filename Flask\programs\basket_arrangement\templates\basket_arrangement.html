<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛒 Basket Arrangement</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <style>
        .basket-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .basket-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .basket-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .basket-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .basket-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
        }

        .status-indicator {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .status-success {
            background: rgba(86, 171, 47, 0.1);
            color: #56ab2f;
            border: 1px solid rgba(86, 171, 47, 0.2);
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .status-warning {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }

        .log-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.5s ease;
        }

        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .basket-container {
                padding: 10px;
            }
            
            .basket-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app" class="basket-container">
        <div class="basket-card">
            <div class="basket-header">
                <h1 class="basket-title">
                    <i class="fas fa-shopping-basket"></i>
                    Basket Arrangement
                </h1>
                <p class="basket-subtitle">Sắp xếp ID thông minh với thuật toán v2.0</p>
                
                <div v-if="currentStatus" class="status-indicator" :class="statusClass">
                    <i :class="statusIcon"></i>
                    {{ currentStatus }}
                </div>
            </div>

            <!-- Sheet Configuration Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-table"></i>
                    Cấu hình Google Sheet
                </h3>
                
                <div class="form-group">
                    <label class="form-label">URL Google Sheet:</label>
                    <input 
                        v-model="sheetUrl" 
                        type="text" 
                        class="form-input"
                        placeholder="Nhập URL hoặc ID của Google Sheet"
                        :disabled="isProcessing"
                    >
                </div>
                
                <button 
                    @click="loadSpreadsheet" 
                    class="btn btn-primary"
                    :disabled="!sheetUrl || isProcessing"
                >
                    <i class="fas fa-download"></i>
                    <span v-if="!isLoadingSheet">Load Spreadsheet</span>
                    <span v-else><div class="loading-spinner"></div> Đang load...</span>
                </button>
            </div>

            <!-- Sheet Settings Section -->
            <div v-if="spreadsheetInfo" class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-cog"></i>
                    Cài đặt Sheet
                </h3>
                
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">Deal List Sheet:</label>
                        <select v-model="config.dealSheetName" class="form-input">
                            <option v-for="sheet in spreadsheetInfo.sheet_names" :key="sheet" :value="sheet">
                                {{ sheet }}
                            </option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Basket Sheet:</label>
                        <select v-model="config.basketSheetName" class="form-input">
                            <option v-for="sheet in spreadsheetInfo.sheet_names" :key="sheet" :value="sheet">
                                {{ sheet }}
                            </option>
                        </select>
                    </div>
                </div>
                
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">Cột đầu tiên:</label>
                        <input v-model="config.firstCol" type="text" class="form-input" placeholder="A">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Số cột ID:</label>
                        <input v-model.number="config.numColumns" type="number" class="form-input" min="1" max="20">
                    </div>
                </div>
            </div>

            <!-- Column Mapping Section -->
            <div v-if="spreadsheetInfo" class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-columns"></i>
                    Mapping Cột Deal List
                </h3>
                
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">Shop ID (cột):</label>
                        <input v-model="columnMapping.shop_id" type="text" class="form-input" placeholder="A">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Item ID (cột):</label>
                        <input v-model="columnMapping.item_id" type="text" class="form-input" placeholder="B">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">GMV (cột):</label>
                        <input v-model="columnMapping.gmv" type="text" class="form-input" placeholder="C">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Review (cột):</label>
                        <input v-model="columnMapping.review" type="text" class="form-input" placeholder="D">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">NO (cột):</label>
                        <input v-model="columnMapping.no" type="text" class="form-input" placeholder="E">
                    </div>
                </div>
            </div>

            <!-- Arrangement Conditions Section -->
            <div v-if="spreadsheetInfo" class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-sort"></i>
                    Điều kiện sắp xếp
                </h3>
                
                <div class="form-group">
                    <label class="form-label">Nhập điều kiện (JSON format):</label>
                    <textarea 
                        v-model="conditionsJson" 
                        class="form-input"
                        rows="6"
                        placeholder='[["Top 100", ["ID1", "ID2"], ["14:00-15:00"], false]]'
                    ></textarea>
                </div>
                
                <button 
                    @click="processArrangement" 
                    class="btn btn-success"
                    :disabled="!conditionsJson || isProcessing"
                >
                    <i class="fas fa-play"></i>
                    <span v-if="!isProcessing">Bắt đầu sắp xếp</span>
                    <span v-else><div class="loading-spinner"></div> Đang xử lý...</span>
                </button>
            </div>

            <!-- Results Section -->
            <div v-if="results" class="results-section">
                <h3 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    Kết quả xử lý
                </h3>
                
                <div class="status-indicator status-success">
                    <i class="fas fa-check-circle"></i>
                    {{ results.message }}
                </div>
                
                <div v-if="results.results" style="margin-top: 15px;">
                    <h4>Chi tiết theo khung giờ:</h4>
                    <div v-for="(result, timeSlot) in results.results" :key="timeSlot" style="margin-bottom: 10px;">
                        <strong>{{ timeSlot }}:</strong>
                        Gốc: {{ result.original_count }}, 
                        Mới: {{ result.new_count }}, 
                        Thêm: {{ result.added_count }}
                    </div>
                </div>
                
                <div v-if="results.logs && results.logs.length > 0" style="margin-top: 20px;">
                    <h4>Log xử lý:</h4>
                    <div class="log-container">
                        <div v-for="(log, index) in results.logs" :key="index" class="log-entry">
                            {{ log }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    sheetUrl: '',
                    spreadsheetInfo: null,
                    config: {
                        dealSheetName: 'Deal list',
                        basketSheetName: 'Basket',
                        firstCol: 'A',
                        numColumns: 12
                    },
                    columnMapping: {
                        shop_id: 'A',
                        item_id: 'B',
                        gmv: 'C',
                        review: 'D',
                        no: 'E'
                    },
                    conditionsJson: '',
                    results: null,
                    currentStatus: '',
                    isLoadingSheet: false,
                    isProcessing: false
                }
            },
            computed: {
                statusClass() {
                    if (this.currentStatus.includes('thành công') || this.currentStatus.includes('Hoàn thành')) {
                        return 'status-success';
                    } else if (this.currentStatus.includes('lỗi') || this.currentStatus.includes('Lỗi')) {
                        return 'status-error';
                    } else if (this.currentStatus.includes('đang') || this.currentStatus.includes('Đang')) {
                        return 'status-warning';
                    }
                    return '';
                },
                statusIcon() {
                    if (this.currentStatus.includes('thành công') || this.currentStatus.includes('Hoàn thành')) {
                        return 'fas fa-check-circle';
                    } else if (this.currentStatus.includes('lỗi') || this.currentStatus.includes('Lỗi')) {
                        return 'fas fa-exclamation-circle';
                    } else if (this.currentStatus.includes('đang') || this.currentStatus.includes('Đang')) {
                        return 'fas fa-clock';
                    }
                    return 'fas fa-info-circle';
                }
            },
            methods: {
                async loadSpreadsheet() {
                    if (!this.sheetUrl) return;
                    
                    this.isLoadingSheet = true;
                    this.currentStatus = 'Đang load spreadsheet...';
                    
                    try {
                        const response = await axios.post('/basket_arrangement/load_spreadsheet', {
                            sheet_url: this.sheetUrl
                        });
                        
                        this.spreadsheetInfo = response.data;
                        this.currentStatus = 'Load spreadsheet thành công';
                        
                        // Auto-select sheets if available
                        if (this.spreadsheetInfo.sheet_names.includes('Deal list')) {
                            this.config.dealSheetName = 'Deal list';
                        }
                        if (this.spreadsheetInfo.sheet_names.includes('Basket')) {
                            this.config.basketSheetName = 'Basket';
                        }
                        
                    } catch (error) {
                        console.error('Error loading spreadsheet:', error);
                        this.currentStatus = 'Lỗi khi load spreadsheet: ' + (error.response?.data?.error || error.message);
                    } finally {
                        this.isLoadingSheet = false;
                    }
                },
                
                async processArrangement() {
                    if (!this.conditionsJson) return;
                    
                    this.isProcessing = true;
                    this.currentStatus = 'Đang xử lý sắp xếp...';
                    this.results = null;
                    
                    try {
                        // Parse conditions
                        const conditions = JSON.parse(this.conditionsJson);
                        
                        const response = await axios.post('/basket_arrangement/process_arrangement', {
                            conditions: conditions,
                            sheet_config: {
                                deal_sheet_name: this.config.dealSheetName,
                                basket_sheet_name: this.config.basketSheetName,
                                first_col: this.config.firstCol,
                                num_columns: this.config.numColumns,
                                column_mapping: this.columnMapping
                            }
                        });
                        
                        this.results = response.data;
                        this.currentStatus = 'Hoàn thành sắp xếp thành công';
                        
                    } catch (error) {
                        console.error('Error processing arrangement:', error);
                        this.currentStatus = 'Lỗi khi xử lý: ' + (error.response?.data?.error || error.message);
                    } finally {
                        this.isProcessing = false;
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
