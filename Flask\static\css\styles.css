/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #ffffff;
    margin: 8px;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 12px 12px 0 0;
    position: relative;
    height: 60px;
    flex-shrink: 0;
}

.logo {
    display: flex;
    align-items: center;
    flex: 1;
}

.program-list-button {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 14px;
    margin-right: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-weight: 500;
    font-size: 0.9rem;
}

.program-list-button:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Remove logo h1 styles - will be moved to footer */

/* Program List Styles */
.program-list {
    position: absolute;
    top: 65px;
    left: 24px;
    width: 380px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    backdrop-filter: blur(20px);
    z-index: 1000;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.program-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.program-list-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.program-items {
    max-height: 500px;
    overflow-y: auto;
    padding: 16px;
}

.program-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.program-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.program-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.3);
}

.program-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.program-icon span {
    font-size: 24px;
    line-height: 1;
}

.program-info h4 {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.program-info p {
    font-size: 0.9rem;
    color: #666;
}

.no-programs {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Main Content Styles */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #fafafa;
    min-height: 0; /* Important for flex child to shrink */
}

/* Tab Bar Styles - Now integrated with header */
.tab-bar {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    height: 40px;
    overflow-x: auto;
    white-space: nowrap;
    flex: 2;
    margin-left: 20px;
    backdrop-filter: blur(10px);
    min-width: 0; /* Allow shrinking */
}

.tab-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    font-style: italic;
}

.tab {
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 100%;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background-color: transparent;
    min-width: 140px;
    max-width: 200px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
    color: white;
    font-size: 0.85rem;
}

.tab:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

.tab.active {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tab span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.85rem;
    font-weight: 500;
}

.tab-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 1rem;
    line-height: 1;
    width: 20px;
    height: 20px;
    text-align: center;
    border-radius: 50%;
    cursor: pointer;
    margin-left: 6px;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.tab-close:hover {
    background-color: rgba(255, 255, 255, 0.3);
    opacity: 1;
    transform: scale(1.1);
}

.tab:not(.active) .tab-close {
    color: #666;
    background: rgba(0, 0, 0, 0.1);
}

.tab:not(.active) .tab-close:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Tab Content Styles */
.tab-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #ffffff;
    min-height: 0; /* Important for flex child to shrink */
}

.tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ffffff;
    display: none;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.tab-pane.active {
    display: flex;
    flex-direction: column;
}

.program-frame {
    width: 100%;
    height: 100%;
    border: none;
    flex: 1;
    background-color: #ffffff;
}

/* Welcome Screen Styles */
.welcome-screen {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 0 0 12px 12px;
}

.welcome-content {
    text-align: center;
    max-width: 700px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.welcome-content h2 {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    font-weight: 700;
}

.welcome-content p {
    font-size: 1.2rem;
    color: #555;
    line-height: 1.8;
    font-weight: 400;
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 12px 24px;
    font-size: 0.9rem;
    color: #6c757d;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.05);
    flex-shrink: 0;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
}

.app-title {
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.program-list {
    animation: slideIn 0.3s ease-out;
}

.tab {
    animation: fadeIn 0.2s ease-out;
}

.program-item {
    animation: fadeIn 0.3s ease-out;
}

/* Scrollbar Styling */
.program-items::-webkit-scrollbar {
    width: 6px;
}

.program-items::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}

.program-items::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
}

.program-items::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Loading state */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Loading spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Desktop-only styles - no mobile responsive needed */