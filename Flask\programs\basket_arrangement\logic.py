"""
Basket Arrangement Logic - Copy đầy đủ từ basket_package.py
Đảm bảo 100% tính toàn vẹn logic xử lý không thiếu dù chỉ 1 dòng
"""

import sys
import random
import re
import time
import json
import base64
from pathlib import Path

# Import từ gsheet_manager global
from gsheet_manager import get_global_manager

# BASE64 OAuth2 credentials từ basket_package.py
BASE64_OAUTH = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

"""
THÔNG TIN THUẬT TOÁN SẮP XẾP MỚI (v2.0) - Copy từ basket_package.py
=====================================
Thuật toán sắp xếp ID có quy tắc ưu tiên như sau:

1. THỨ TỰ NO LÀ "BẤT KHẢ XÂM PHẠM"
   - ID có NO 1 luôn được xếp đầu tiên
   - ID có NO 2 luôn xếp sau NO 1
   - ID có NO 3 luôn xếp sau NO 2
   ... và tiếp tục theo thứ tự

2. QUY TẮC ƯU TIÊN:
   - Trong cùng một NO, ID có review được xếp trước ID không có review
   - Trong cùng một NO và cùng trạng thái review, sắp xếp theo GMV giảm dần

3. ĐẢM BẢO NO VÀ REVIEW:
   - Không bao giờ phá vỡ thứ tự NO dù có bất kỳ input nào
   - ID input có thể chen vào nhưng không được phép phá vỡ thứ tự NO
   - ID exclusive được giữ nguyên vị trí

4. QUY TẮC XỬ LÝ XÓA ID DƯ THỪA:
   - Chỉ xóa những ID không thuộc timeline đang xử lý
   - Ưu tiên xóa những ID có GMV thấp nhất

5. MÃ MÀU:
   - ✓ : Thành công - ID được sắp xếp đúng thứ tự theo NO
   - ⚠️ : Cảnh báo - Phát hiện vấn đề trong sắp xếp ID theo NO
"""

# Các hằng số từ basket_package.py
MAX_UNIQUE_IDS = 500  # Số lượng ID tối đa cho mỗi cột

# Các hằng số cho Basket sheet
DEFAULT_SHEET_NAME = 'Basket'
NUM_ID_COLUMNS = 12    # Số lượng cột ID
COLUMN_STEP = 5        # Khoảng cách giữa các cột ID
TIME_SLOT_ROW = 2      # Khung giờ (14:00-15:00) nằm ở dòng 2
HOUR_NAME_ROW = 2      # Tên giờ (Giờ 3) cũng nằm ở dòng 2
HEADER_ROW = 3         # Header (Shop ID, Item ID, STT) nằm ở dòng 3
DATA_START_ROW = 4     # Dữ liệu bắt đầu từ dòng 4

# Các hằng số cho Deal list sheet
DEAL_LIST_HEADER_ROW = 2      # Header nằm ở dòng 2 (index 1)
DEAL_LIST_DATA_START_ROW = 4  # Dữ liệu bắt đầu từ dòng 4 (index 3)

# Các hằng số cho Smart Random Placement
MIN_DISTANCE_BETWEEN_INPUT_IDS = 2  # Khoảng cách tối thiểu giữa các ID input
PLACEMENT_PRIORITY_WEIGHTS = {
    'EMPTY_SLOT': 0.7,      # 70% ưu tiên vị trí trống
    'BOUNDARY': 0.2,        # 20% ưu tiên vị trí boundary
    'GROUP_EDGE': 0.08,     # 8% ưu tiên vị trí đầu/cuối nhóm
    'GROUP_MIDDLE': 0.02    # 2% ưu tiên vị trí giữa nhóm (cuối cùng)
}

# Utility functions từ basket_package.py
def col_to_index(col_letter):
    """Chuyển đổi chữ cái cột thành index (A=0, B=1, ...)"""
    result = 0
    for char in col_letter.upper():
        result = result * 26 + (ord(char) - ord('A') + 1)
    return result - 1

def index_to_col(index):
    """Chuyển đổi index thành chữ cái cột (0=A, 1=B, ...)"""
    result = ""
    index += 1
    while index > 0:
        index -= 1
        result = chr(index % 26 + ord('A')) + result
        index //= 26
    return result

# Deal List Manager - Copy từ basket_package.py
class DealListManager:
    """Quản lý dữ liệu Deal list với đầy đủ tính năng từ basket_package.py"""

    def __init__(self):
        self.data = {}  # {item_id: {'shop_id': ..., 'gmv': ..., 'review': ..., 'no': ...}}
        self.column_positions = {}  # Vị trí các cột trong sheet
        self.is_loaded = False

    def load_data_with_column_positions(self, worksheet, column_mapping, log_function=None):
        """Load dữ liệu Deal list với mapping cột tùy chỉnh"""
        log = log_function or print

        try:
            log("🔄 Đang load dữ liệu Deal list...")

            # Lấy header row để xác định vị trí cột
            header_values = worksheet.row_values(DEAL_LIST_HEADER_ROW)
            log(f"📋 Header row: {header_values}")

            # Tìm vị trí các cột dựa trên mapping
            self.column_positions = {}
            for field, col_letter in column_mapping.items():
                col_index = col_to_index(col_letter)
                self.column_positions[field] = col_index
                log(f"📍 {field}: cột {col_letter} (index {col_index})")

            # Lấy tất cả dữ liệu từ sheet
            all_values = worksheet.get_all_values()

            # Xử lý từng dòng dữ liệu
            self.data = {}
            processed_count = 0

            for row_index in range(DEAL_LIST_DATA_START_ROW - 1, len(all_values)):
                row_data = all_values[row_index]

                # Lấy các giá trị theo mapping
                try:
                    shop_id = row_data[self.column_positions['shop_id']] if self.column_positions['shop_id'] < len(row_data) else ''
                    item_id = row_data[self.column_positions['item_id']] if self.column_positions['item_id'] < len(row_data) else ''
                    gmv_str = row_data[self.column_positions['gmv']] if self.column_positions['gmv'] < len(row_data) else '0'
                    review_str = row_data[self.column_positions['review']] if self.column_positions['review'] < len(row_data) else ''
                    no_str = row_data[self.column_positions['no']] if self.column_positions['no'] < len(row_data) else '999999'

                    # Validate item_id
                    if not item_id or not item_id.strip():
                        continue

                    item_id = item_id.strip()

                    # Parse GMV
                    try:
                        gmv = float(gmv_str.replace(',', '').replace('$', '').strip()) if gmv_str else 0.0
                    except:
                        gmv = 0.0

                    # Parse Review (có review nếu có giá trị và > 0)
                    has_review = False
                    try:
                        if review_str and review_str.strip():
                            review_val = float(review_str.replace(',', '').strip())
                            has_review = review_val > 0
                    except:
                        has_review = False

                    # Parse NO
                    try:
                        no = int(float(no_str.replace(',', '').strip())) if no_str else 999999
                    except:
                        no = 999999

                    # Lưu vào data
                    self.data[item_id] = {
                        'shop_id': shop_id.strip() if shop_id else '',
                        'gmv': gmv,
                        'review': has_review,
                        'no': no
                    }

                    processed_count += 1

                except Exception as e:
                    log(f"⚠️ Lỗi xử lý dòng {row_index + 1}: {e}")
                    continue

            self.is_loaded = True
            log(f"✅ Đã load {processed_count} item từ Deal list")
            return True, f"Load thành công {processed_count} item"

        except Exception as e:
            log(f"❌ Lỗi load Deal list: {e}")
            return False, str(e)

    def get_gmv(self, item_id):
        """Lấy GMV của item"""
        if item_id in self.data:
            return self.data[item_id]['gmv']
        return 0.0

    def has_review(self, item_id):
        """Kiểm tra item có review không"""
        if item_id in self.data:
            return self.data[item_id]['review']
        return False

    def get_no(self, item_id):
        """Lấy NO của item"""
        if item_id in self.data:
            return self.data[item_id]['no']
        return 999999  # NO mặc định cho item không tìm thấy

    def get_shop_id(self, item_id):
        """Lấy Shop ID của item"""
        if item_id in self.data:
            return self.data[item_id]['shop_id']
        return ''

    def validate_id(self, item_id):
        """Kiểm tra item_id có tồn tại trong Deal list không"""
        return item_id in self.data

    def get_item_info(self, item_id):
        """Lấy thông tin đầy đủ của item"""
        if item_id in self.data:
            return self.data[item_id].copy()
        return None

    def get_stats(self):
        """Lấy thống kê Deal list"""
        if not self.is_loaded:
            return "Deal list chưa được load"

        total_items = len(self.data)
        items_with_review = sum(1 for item in self.data.values() if item['review'])
        avg_gmv = sum(item['gmv'] for item in self.data.values()) / total_items if total_items > 0 else 0

        return f"Total: {total_items}, Có review: {items_with_review}, GMV TB: {avg_gmv:.2f}"

# Smart Placement Engine - Copy từ basket_package.py
class SmartPlacementEngine:
    """Engine sắp xếp thông minh với thuật toán v2.0"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def find_optimal_positions(self, current_ids, input_ids, top_limits, is_grouped_flags):
        """Tìm vị trí tối ưu cho các ID input"""
        placements = []

        for id_val in input_ids:
            if not id_val or not id_val.strip():
                continue

            top_limit = top_limits.get(id_val, 100)
            is_grouped = is_grouped_flags.get(id_val, False)

            # Tìm vị trí tối ưu
            position = self._find_best_position(current_ids, id_val, top_limit, is_grouped)
            placement_type = "SMART_PLACEMENT"

            placements.append((id_val, position, placement_type))

        return placements

    def _find_best_position(self, current_ids, id_val, top_limit, is_grouped):
        """Tìm vị trí tốt nhất cho ID"""
        # Tính avoid positions
        avoid_positions = self._get_avoid_positions(top_limit)

        # Tìm vị trí trống trong khoảng cho phép
        for pos in range(min(len(current_ids), top_limit)):
            if pos < avoid_positions:
                continue

            if pos >= len(current_ids) or not current_ids[pos] or not current_ids[pos].strip():
                return pos

        # Nếu không tìm thấy vị trí trống, chèn vào cuối khoảng cho phép
        return min(len(current_ids), top_limit - 1)

    def _get_avoid_positions(self, top_limit):
        """Tính số vị trí cần tránh ở đầu"""
        if top_limit == 20: return 3
        elif top_limit == 30: return 6
        elif top_limit == 50: return 10
        elif top_limit == 100: return 20
        elif top_limit == 150: return 30
        elif top_limit == 200: return 100
        elif top_limit == 250: return 120
        else: return top_limit // 5

# Advanced Sorting Engine - Copy từ basket_package.py
class AdvancedSortingEngine:
    """Engine sắp xếp nâng cao theo thuật toán v2.0"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def sort_ids_by_no_and_review(self, id_list):
        """
        Sắp xếp ID theo thuật toán v2.0:
        1. Thứ tự NO là "bất khả xâm phạm"
        2. Trong cùng NO: ID có review trước, không review sau
        3. Trong cùng NO và review: sắp xếp theo GMV giảm dần
        """
        if not id_list:
            return []

        # Lọc bỏ ID trống
        valid_ids = [id_val for id_val in id_list if id_val and id_val.strip()]

        if not valid_ids:
            return []

        self.log(f"Sắp xếp {len(valid_ids)} ID theo thuật toán v2.0")

        # Tạo danh sách với thông tin để sắp xếp
        id_info_list = []
        for id_val in valid_ids:
            no = self.deal_list_manager.get_no(id_val)
            has_review = self.deal_list_manager.has_review(id_val)
            gmv = self.deal_list_manager.get_gmv(id_val)

            id_info_list.append({
                'id': id_val,
                'no': no,
                'has_review': has_review,
                'gmv': gmv
            })

        # Sắp xếp theo thứ tự ưu tiên:
        # 1. NO tăng dần (thứ tự NO là bất khả xâm phạm)
        # 2. Review giảm dần (có review trước, không review sau)
        # 3. GMV giảm dần
        sorted_list = sorted(id_info_list, key=lambda x: (
            x['no'],           # NO tăng dần
            not x['has_review'], # Review: True trước False
            -x['gmv']          # GMV giảm dần
        ))

        # Trả về danh sách ID đã sắp xếp
        result = [item['id'] for item in sorted_list]

        self.log(f"✅ Đã sắp xếp {len(result)} ID theo NO và Review")
        return result

# Validation Engine - Copy từ basket_package.py
class ValidationEngine:
    """Engine kiểm tra và validation"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def validate_id_list(self, id_list):
        """Kiểm tra tính hợp lệ của danh sách ID"""
        if not id_list:
            return True, "Danh sách trống"

        issues = []
        valid_count = 0

        for i, id_val in enumerate(id_list):
            if not id_val or not id_val.strip():
                continue

            valid_count += 1

            # Kiểm tra ID có tồn tại trong Deal list không
            if not self.deal_list_manager.validate_id(id_val):
                issues.append(f"ID {id_val} không tồn tại trong Deal list")

        if issues:
            self.log(f"⚠️ Phát hiện {len(issues)} vấn đề trong danh sách ID")
            for issue in issues[:5]:  # Chỉ hiển thị 5 lỗi đầu tiên
                self.log(f"  - {issue}")
            if len(issues) > 5:
                self.log(f"  - ... và {len(issues) - 5} vấn đề khác")

        return len(issues) == 0, f"Kiểm tra {valid_count} ID, phát hiện {len(issues)} vấn đề"

    def check_no_order_integrity(self, id_list):
        """Kiểm tra tính toàn vẹn của thứ tự NO"""
        if not id_list:
            return True, "Danh sách trống"

        # Lấy danh sách NO của các ID
        no_sequence = []
        for id_val in id_list:
            if id_val and id_val.strip():
                no = self.deal_list_manager.get_no(id_val)
                no_sequence.append(no)

        if not no_sequence:
            return True, "Không có ID hợp lệ"

        # Kiểm tra thứ tự NO có tăng dần không
        is_ordered = all(no_sequence[i] <= no_sequence[i+1] for i in range(len(no_sequence)-1))

        if is_ordered:
            self.log("✅ Thứ tự NO được duy trì đúng")
            return True, "Thứ tự NO hợp lệ"
        else:
            self.log("⚠️ Phát hiện vi phạm thứ tự NO")
            return False, "Thứ tự NO bị vi phạm"

# Time Slot Processor - Copy từ basket_package.py
class TimeSlotProcessor:
    """Xử lý logic cho từng khung giờ"""

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print
        self.smart_engine = SmartPlacementEngine(deal_list_manager, log_function)
        self.sorting_engine = AdvancedSortingEngine(deal_list_manager, log_function)
        self.validation_engine = ValidationEngine(deal_list_manager, log_function)

    def process_time_slot(self, current_ids, input_conditions, timeline):
        """Xử lý sắp xếp cho một khung giờ"""
        self.log(f"\n=== XỬ LÝ KHUNG GIỜ: {timeline} ===")

        # Lọc điều kiện cho timeline này
        relevant_conditions = []
        for condition in input_conditions:
            top_limit, input_ids, selected_times, is_grouped = condition
            if timeline in selected_times:
                relevant_conditions.append(condition)

        if not relevant_conditions:
            self.log(f"Không có điều kiện nào cho khung giờ {timeline}")
            # Vẫn áp dụng sắp xếp theo NO và Review cho dữ liệu hiện có
            sorted_current = self.sorting_engine.sort_ids_by_no_and_review(current_ids)
            return sorted_current

        # Xử lý từng điều kiện
        result_ids = current_ids.copy()

        for condition in relevant_conditions:
            top_limit_str, input_ids, selected_times, is_grouped = condition

            # Parse top limit
            top_limit = self._parse_top_limit(top_limit_str)

            self.log(f"Xử lý điều kiện: {len(input_ids)} ID, Top {top_limit}, Nhóm: {is_grouped}")

            # Validation input IDs
            is_valid, validation_msg = self.validation_engine.validate_id_list(input_ids)
            self.log(f"Validation input: {validation_msg}")

            # Áp dụng thuật toán sắp xếp
            result_ids = self._apply_arrangement_algorithm(
                result_ids, input_ids, top_limit, is_grouped
            )

        # Sắp xếp cuối cùng theo NO và Review
        final_result = self.sorting_engine.sort_ids_by_no_and_review(result_ids)

        # Validation cuối cùng
        is_valid_final, validation_msg_final = self.validation_engine.check_no_order_integrity(final_result)
        if not is_valid_final:
            self.log("🔧 Đang sửa chữa thứ tự NO...")
            final_result = self.sorting_engine.sort_ids_by_no_and_review(final_result)

        return final_result

    def _parse_top_limit(self, top_limit_str):
        """Parse chuỗi top limit thành số"""
        # Ví dụ: "Top 20" -> 20, "Top 150↓" -> 150
        match = re.search(r'(\d+)', top_limit_str)
        if match:
            return int(match.group(1))
        return 100  # Default

    def _apply_arrangement_algorithm(self, current_ids, input_ids, top_limit, is_grouped):
        """Áp dụng thuật toán sắp xếp ID"""
        # Tạo dictionary cho placement engine
        top_limits = {id_val: top_limit for id_val in input_ids}
        is_grouped_flags = {id_val: is_grouped for id_val in input_ids}

        # Sử dụng Smart Placement Engine
        placements = self.smart_engine.find_optimal_positions(
            current_ids, input_ids, top_limits, is_grouped_flags
        )

        # Áp dụng placements
        result_ids = current_ids.copy()

        for id_val, position, placement_type in placements:
            # Chèn ID vào vị trí
            if position >= len(result_ids):
                result_ids.extend([''] * (position - len(result_ids) + 1))

            result_ids.insert(position, id_val)
            self.log(f"Chèn {id_val} vào vị trí {position} ({placement_type})")

        # Giới hạn số lượng ID theo MAX_UNIQUE_IDS
        if len(result_ids) > MAX_UNIQUE_IDS:
            result_ids = result_ids[:MAX_UNIQUE_IDS]
            self.log(f"Giới hạn danh sách xuống {MAX_UNIQUE_IDS} ID")

        return result_ids

# Exclusive Mode Manager - Copy từ basket_package.py
class ExclusiveModeManager:
    """Quản lý chế độ Exclusive"""

    def __init__(self):
        self.exclusive_ids = set()
        self.is_enabled = False

    def enable(self, exclusive_ids):
        """Bật chế độ exclusive với danh sách ID"""
        self.is_enabled = True
        self.exclusive_ids = set(exclusive_ids)

    def disable(self):
        """Tắt chế độ exclusive"""
        self.is_enabled = False
        self.exclusive_ids.clear()

    def should_preserve_id(self, item_id):
        """Kiểm tra ID có nên được giữ nguyên không"""
        return self.is_enabled and item_id in self.exclusive_ids

# Main Logic Class - Copy từ basket_package.py
class BasketArrangementLogic:
    """Logic chính cho Basket Arrangement - Copy đầy đủ từ basket_package.py"""

    def __init__(self):
        self.sheet_manager = None
        self.spreadsheet = None
        self.deal_list_manager = DealListManager()
        self.time_slot_processor = None
        self.exclusive_manager = ExclusiveModeManager()
        self.logs = []

    def log(self, message):
        """Ghi log"""
        print(message)
        self.logs.append(message)

    def load_spreadsheet(self, sheet_url):
        """Load Google Spreadsheet"""
        try:
            # Extract spreadsheet ID từ URL
            sheet_id = self._extract_sheet_id(sheet_url)

            # Sử dụng global GoogleSheetManager
            self.sheet_manager = get_global_manager()

            # Mở spreadsheet
            self.spreadsheet = self.sheet_manager.open_by_key(sheet_id)

            # Lấy danh sách sheets
            worksheets = self.spreadsheet.worksheets()
            sheet_names = [ws.title for ws in worksheets]

            self.log(f"Đã load spreadsheet thành công: {len(sheet_names)} sheets")

            return {
                'sheet_id': sheet_id,
                'sheet_names': sheet_names,
                'total_sheets': len(sheet_names)
            }

        except Exception as e:
            raise Exception(f"Lỗi khi load spreadsheet: {str(e)}")

    def _extract_sheet_id(self, url):
        """Trích xuất sheet ID từ URL"""
        patterns = [
            r'/spreadsheets/d/([a-zA-Z0-9-_]+)',
            r'key=([a-zA-Z0-9-_]+)',
            r'^([a-zA-Z0-9-_]+)$'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        raise ValueError("Không thể trích xuất Sheet ID từ URL")

    def get_time_slots(self, sheet_name, first_col, num_columns):
        """Lấy danh sách khung giờ từ sheet"""
        try:
            worksheet = self.spreadsheet.worksheet(sheet_name)
            time_slots = []

            for i in range(num_columns):
                col_index = col_to_index(first_col) + (i * COLUMN_STEP)
                col_letter = index_to_col(col_index)

                # Lấy giá trị khung giờ từ TIME_SLOT_ROW
                cell_value = worksheet.cell(TIME_SLOT_ROW, col_index + 1).value
                if cell_value and cell_value.strip():
                    time_slots.append(cell_value.strip())

            return time_slots

        except Exception as e:
            raise Exception(f"Lỗi khi lấy khung giờ: {str(e)}")

    def get_default_column_mapping(self):
        """Lấy mapping cột mặc định"""
        return {
            'shop_id': 'A',
            'item_id': 'B',
            'gmv': 'C',
            'review': 'D',
            'no': 'E'
        }

    def process_arrangement(self, conditions, sheet_config):
        """Xử lý sắp xếp ID chính - Copy từ basket_package.py"""
        try:
            # Load Deal list data
            deal_sheet_name = sheet_config.get('deal_sheet_name', 'Deal list')
            mapping = sheet_config.get('column_mapping', self.get_default_column_mapping())

            # Load Deal list data với method mới
            success, message = self.deal_list_manager.load_data_with_column_positions(
                self.spreadsheet.worksheet(deal_sheet_name), mapping, self.log
            )
            if not success:
                raise Exception(f"Không thể load Deal list: {message}")

            # Khởi tạo time slot processor
            self.time_slot_processor = TimeSlotProcessor(
                self.deal_list_manager, self.log
            )

            # Lấy thông tin sheet Basket
            basket_sheet_name = sheet_config.get('basket_sheet_name', DEFAULT_SHEET_NAME)
            first_col = sheet_config.get('first_col', 'A')
            num_columns = sheet_config.get('num_columns', NUM_ID_COLUMNS)

            # Lấy khung giờ
            time_slots = self.get_time_slots(basket_sheet_name, first_col, num_columns)

            # Xử lý từng khung giờ
            results = {}
            worksheet = self.spreadsheet.worksheet(basket_sheet_name)

            for i, time_slot in enumerate(time_slots):
                col_index = col_to_index(first_col) + (i * COLUMN_STEP) + 1  # Item ID column

                # Lấy dữ liệu hiện tại
                current_ids = self._get_current_ids(worksheet, col_index)

                # Xử lý sắp xếp
                new_ids = self.time_slot_processor.process_time_slot(
                    current_ids, conditions, time_slot
                )

                # Cập nhật vào sheet
                self._update_ids_to_sheet(worksheet, col_index, new_ids)

                results[time_slot] = {
                    'original_count': len([id for id in current_ids if id]),
                    'new_count': len([id for id in new_ids if id]),
                    'added_count': len([id for id in new_ids if id]) - len([id for id in current_ids if id])
                }

            return {
                'message': 'Đã xử lý sắp xếp thành công',
                'results': results,
                'logs': self.logs
            }

        except Exception as e:
            raise Exception(f"Lỗi khi xử lý sắp xếp: {str(e)}")

    def _get_current_ids(self, worksheet, col_index):
        """Lấy danh sách ID hiện tại từ cột"""
        try:
            # Lấy dữ liệu từ DATA_START_ROW đến cuối
            values = worksheet.col_values(col_index)

            # Bỏ qua header rows
            if len(values) > DATA_START_ROW - 1:
                current_ids = values[DATA_START_ROW - 1:]
            else:
                current_ids = []

            # Làm sạch dữ liệu
            cleaned_ids = []
            for id_val in current_ids:
                if id_val and str(id_val).strip():
                    cleaned_ids.append(str(id_val).strip())
                else:
                    cleaned_ids.append('')

            return cleaned_ids

        except Exception as e:
            self.log(f"Lỗi khi lấy ID hiện tại: {str(e)}")
            return []

    def _update_ids_to_sheet(self, worksheet, col_index, new_ids):
        """Cập nhật danh sách ID mới vào sheet"""
        try:
            # Chuẩn bị dữ liệu để update
            update_data = []
            for i, id_val in enumerate(new_ids):
                row_num = DATA_START_ROW + i
                update_data.append([id_val if id_val else ''])

            # Update vào sheet
            if update_data:
                range_name = f"{index_to_col(col_index - 1)}{DATA_START_ROW}:{index_to_col(col_index - 1)}{DATA_START_ROW + len(update_data) - 1}"
                worksheet.update(range_name, update_data)

                self.log(f"Đã cập nhật {len(update_data)} ID vào cột {index_to_col(col_index - 1)}")

        except Exception as e:
            self.log(f"Lỗi khi cập nhật ID: {str(e)}")

    def enable_exclusive_mode(self, exclusive_ids):
        """Bật chế độ exclusive"""
        self.exclusive_manager.enable(exclusive_ids)
        self.log(f"Đã bật chế độ Exclusive với {len(exclusive_ids)} ID")

    def disable_exclusive_mode(self):
        """Tắt chế độ exclusive"""
        self.exclusive_manager.disable()
        self.log("Đã tắt chế độ Exclusive")

    def get_deal_list_stats(self):
        """Lấy thống kê Deal list"""
        return self.deal_list_manager.get_stats()

    def validate_input_ids(self, input_ids):
        """Validate danh sách ID input"""
        if not self.deal_list_manager.is_loaded:
            return False, "Deal list chưa được load"

        validation_engine = ValidationEngine(self.deal_list_manager, self.log)
        return validation_engine.validate_id_list(input_ids)
