"""
Core modules cho Basket Arrangement
Chứa toàn bộ logic được copy từ basket_package.py và gsheet_manager.py
"""

from .constants import *
# Import global gsheet manager
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from gsheet_manager import get_global_manager as get_global_gsheet_manager
from .smart_placement import SmartPlacementEngine
from .deal_list_manager import DealListManager
from .time_slot_processor import TimeSlotProcessor

__all__ = [
    'get_global_gsheet_manager',
    'SmartPlacementEngine',
    'DealListManager',
    'TimeSlotProcessor',
    'col_to_index',
    'index_to_col',
    'normalize_timeline',
    'MAX_UNIQUE_IDS',
    'DEFAULT_SHEET_NAME',
    'NUM_ID_COLUMNS',
    'COLUMN_STEP',
    'TIME_SLOT_ROW',
    'HOUR_NAME_ROW',
    'HEADER_ROW',
    'DATA_START_ROW',
    'DEAL_LIST_HEADER_ROW',
    'DEAL_LIST_DATA_START_ROW',
    'MIN_DISTANCE_BETWEEN_INPUT_IDS',
    'PLACEMENT_PRIORITY_WEIGHTS',
    'BASE64_OAUTH'
]
