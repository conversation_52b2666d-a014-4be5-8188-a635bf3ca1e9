"""
Global Google Sheets Manager cho Data Assortment All in One
Quản lý kết nối và thao tác với Google Sheets API cho tất cả các chương trình
"""

import os
import json
import base64
import logging
import tempfile
from typing import Optional, Dict, Any, List
import gspread
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import Flow

class GlobalGoogleSheetManager:
    """Quản lý kết nối Google Sheets toàn cục cho tất cả chương trình"""

    _instance = None
    _initialized = False

    def __new__(cls):
        """Singleton pattern để đảm bảo chỉ có một instance"""
        if cls._instance is None:
            cls._instance = super(GlobalGoogleSheetManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Khởi tạo GlobalGoogleSheetManager"""
        if self._initialized:
            return

        self.gc = None
        self.credentials = None
        self.token_file = os.path.join(
            os.environ.get('LOCALAPPDATA', os.path.expanduser('~')),
            'Data All in One',
            'Global',
            'token.json'
        )

        # Tạo thư mục lưu token nếu chưa có
        os.makedirs(os.path.dirname(self.token_file), exist_ok=True)

        # Thiết lập logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Danh sách credentials để thử (từ các chương trình khác nhau)
        self.credentials_list = [
            # External Update credentials
            "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            # Import data credentials
            "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        ]

        # Credentials hiện tại đang sử dụng
        self.current_credentials_base64 = self.credentials_list[0]

        self._initialized = True

    def _decode_credentials(self, credentials_base64: str = None) -> Dict[str, Any]:
        """Giải mã credentials từ base64"""
        try:
            creds_b64 = credentials_base64 or self.current_credentials_base64
            if not creds_b64:
                raise ValueError("Credentials base64 không được cung cấp")

            decoded_bytes = base64.b64decode(creds_b64)
            credentials_info = json.loads(decoded_bytes.decode('utf-8'))
            return credentials_info
        except Exception as e:
            self.logger.error(f"Lỗi khi giải mã credentials: {str(e)}")
            raise

    def _load_token(self) -> Optional[Credentials]:
        """Tải token từ file"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r') as f:
                    token_data = json.load(f)

                credentials = Credentials(
                    token=token_data.get('token'),
                    refresh_token=token_data.get('refresh_token'),
                    token_uri=token_data.get('token_uri'),
                    client_id=token_data.get('client_id'),
                    client_secret=token_data.get('client_secret'),
                    scopes=token_data.get('scopes')
                )

                # Refresh token nếu cần
                if credentials.expired and credentials.refresh_token:
                    credentials.refresh(Request())
                    self._save_token(credentials)

                return credentials
        except Exception as e:
            self.logger.warning(f"Không thể tải token: {str(e)}")

        return None

    def _save_token(self, credentials: Credentials):
        """Lưu token vào file"""
        try:
            token_data = {
                'token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_uri': credentials.token_uri,
                'client_id': credentials.client_id,
                'client_secret': credentials.client_secret,
                'scopes': credentials.scopes
            }

            with open(self.token_file, 'w') as f:
                json.dump(token_data, f, indent=2)

            self.logger.info("Token đã được lưu thành công")
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu token: {str(e)}")

    def authenticate(self) -> bool:
        """
        Xác thực với Google Sheets API

        Returns:
            bool: True nếu xác thực thành công
        """
        try:
            # Thử tải token hiện có
            self.credentials = self._load_token()

            if self.credentials and self.credentials.valid:
                self.gc = gspread.authorize(self.credentials)
                self.logger.info("Xác thực thành công với token hiện có")
                return True

            # Nếu không có token hợp lệ, cần OAuth flow (sẽ được xử lý qua web interface)
            self.logger.warning("Cần xác thực OAuth qua web interface")
            return False

        except Exception as e:
            self.logger.error(f"Lỗi xác thực: {str(e)}")
            return False

    def force_authenticate(self) -> bool:
        """
        Force authenticate - gọi lại authenticate và cập nhật trạng thái

        Returns:
            bool: True nếu xác thực thành công
        """
        result = self.authenticate()
        if result:
            self.logger.info("Force authenticate thành công")
        else:
            self.logger.warning("Force authenticate thất bại")
        return result

    def get_auth_url(self, credentials_base64: str = None) -> str:
        """
        Lấy URL xác thực OAuth (để sử dụng trong web interface)

        Args:
            credentials_base64: Credentials cụ thể để sử dụng (optional)

        Returns:
            str: URL xác thực
        """
        try:
            credentials_info = self._decode_credentials(credentials_base64)

            # Tạo file credentials tạm thời
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(credentials_info, f)
                temp_creds_file = f.name

            try:
                # Thiết lập OAuth flow
                scopes = [
                    'https://www.googleapis.com/auth/spreadsheets',
                    'https://www.googleapis.com/auth/drive'
                ]

                flow = Flow.from_client_secrets_file(
                    temp_creds_file,
                    scopes=scopes,
                    redirect_uri='http://localhost:5000/oauth/callback'
                )

                # Lưu flow để sử dụng sau
                self._temp_flow = flow
                self._temp_creds_file = temp_creds_file

                # Lấy authorization URL
                auth_url, _ = flow.authorization_url(prompt='consent')
                return auth_url

            except Exception as e:
                # Xóa file nếu có lỗi
                if os.path.exists(temp_creds_file):
                    os.unlink(temp_creds_file)
                raise

        except Exception as e:
            self.logger.error(f"Lỗi khi tạo auth URL: {str(e)}")
            raise

    def complete_oauth_flow(self, auth_code: str) -> bool:
        """
        Hoàn thành OAuth flow với authorization code

        Args:
            auth_code: Authorization code từ Google

        Returns:
            bool: True nếu thành công
        """
        try:
            if not hasattr(self, '_temp_flow'):
                raise RuntimeError("OAuth flow chưa được khởi tạo. Gọi get_auth_url() trước.")

            # Lấy token
            self._temp_flow.fetch_token(code=auth_code)
            self.credentials = self._temp_flow.credentials

            # Lưu token
            self._save_token(self.credentials)

            # Tạo gspread client
            self.gc = gspread.authorize(self.credentials)

            self.logger.info("Xác thực OAuth thành công")

            # Test connection để đảm bảo hoạt động
            try:
                # Test bằng cách tạo một request đơn giản
                test_result = self.test_connection()
                if test_result:
                    self.logger.info("Test connection thành công")
                else:
                    self.logger.warning("Test connection thất bại")
            except Exception as e:
                self.logger.warning(f"Test connection lỗi: {str(e)}")

            # Cleanup
            if hasattr(self, '_temp_creds_file') and os.path.exists(self._temp_creds_file):
                os.unlink(self._temp_creds_file)

            return True

        except Exception as e:
            self.logger.error(f"Lỗi OAuth flow: {str(e)}")
            return False

    def open_by_key(self, spreadsheet_id: str):
        """
        Mở spreadsheet bằng ID

        Args:
            spreadsheet_id: ID của spreadsheet

        Returns:
            Spreadsheet object
        """
        if not self.gc:
            raise RuntimeError("Chưa xác thực với Google Sheets API")

        try:
            return self.gc.open_by_key(spreadsheet_id)
        except Exception as e:
            self.logger.error(f"Lỗi khi mở spreadsheet {spreadsheet_id}: {str(e)}")
            raise

    def open_by_url(self, url: str):
        """
        Mở spreadsheet bằng URL

        Args:
            url: URL của spreadsheet

        Returns:
            Spreadsheet object
        """
        if not self.gc:
            raise RuntimeError("Chưa xác thực với Google Sheets API")

        try:
            return self.gc.open_by_url(url)
        except Exception as e:
            self.logger.error(f"Lỗi khi mở spreadsheet từ URL: {str(e)}")
            raise

    def get_sheets_service(self):
        """
        Lấy Google Sheets service object

        Returns:
            Google Sheets service object
        """
        if not self.credentials:
            raise RuntimeError("Chưa xác thực với Google Sheets API")

        try:
            from googleapiclient.discovery import build
            service = build('sheets', 'v4', credentials=self.credentials)
            return service
        except Exception as e:
            self.logger.error(f"Lỗi khi tạo Sheets service: {str(e)}")
            raise

    def test_connection(self) -> bool:
        """
        Test kết nối với Google Sheets API

        Returns:
            bool: True nếu kết nối thành công
        """
        try:
            if not self.gc:
                return False

            # Thử lấy danh sách spreadsheets (chỉ để test)
            # Không thực sự lấy vì có thể không có quyền
            return True

        except Exception as e:
            self.logger.error(f"Test kết nối thất bại: {str(e)}")
            return False

    def is_authenticated(self) -> bool:
        """
        Kiểm tra xem đã xác thực chưa

        Returns:
            bool: True nếu đã xác thực
        """
        return self.gc is not None and self.credentials is not None

    def get_credentials_info(self) -> Dict[str, Any]:
        """
        Lấy thông tin credentials hiện tại

        Returns:
            Dict chứa thông tin credentials
        """
        if not self.credentials:
            return {}

        return {
            'client_id': getattr(self.credentials, 'client_id', None),
            'scopes': getattr(self.credentials, 'scopes', []),
            'expired': getattr(self.credentials, 'expired', True),
            'valid': getattr(self.credentials, 'valid', False)
        }

    def try_alternative_credentials(self) -> bool:
        """
        Thử các credentials thay thế nếu credentials hiện tại thất bại

        Returns:
            bool: True nếu tìm được credentials hoạt động
        """
        for i, alt_creds in enumerate(self.credentials_list):
            if alt_creds == self.current_credentials_base64:
                continue  # Skip current credentials

            try:
                self.logger.info(f"Thử credentials thay thế {i+1}/{len(self.credentials_list)}")

                # Tạm thời thay đổi credentials
                old_creds = self.current_credentials_base64
                self.current_credentials_base64 = alt_creds

                # Thử xác thực
                if self.authenticate():
                    self.logger.info(f"Thành công với credentials thay thế {i+1}")
                    return True
                else:
                    # Khôi phục credentials cũ nếu thất bại
                    self.current_credentials_base64 = old_creds

            except Exception as e:
                self.logger.warning(f"Credentials thay thế {i+1} thất bại: {str(e)}")
                # Khôi phục credentials cũ
                self.current_credentials_base64 = old_creds
                continue

        return False

# Global instance
_global_manager = None

def get_global_manager() -> GlobalGoogleSheetManager:
    """
    Lấy instance global của GoogleSheetManager

    Returns:
        GlobalGoogleSheetManager: Instance duy nhất
    """
    global _global_manager
    if _global_manager is None:
        _global_manager = GlobalGoogleSheetManager()
    return _global_manager

# Utility functions để tương thích với code cũ
def extract_spreadsheet_id(link: str) -> str:
    """Trích xuất Spreadsheet ID từ URL Google Sheet"""
    # Xử lý các dạng URL khác nhau
    if not link:
        return ""

    # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
    if '/' not in link and '.' not in link and len(link) >= 25:
        return link

    # Trích xuất từ full URL
    if '/d/' in link:
        # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
        id_part = link.split('/d/')[1]
        return id_part.split('/')[0].split('?')[0].split('#')[0]
    elif 'spreadsheets/d/' in link:
        # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
        id_part = link.split('spreadsheets/d/')[1]
        return id_part.split('/')[0].split('?')[0].split('#')[0]
    elif 'key=' in link:
        # Format: https://docs.google.com/spreadsheets/d/key=SPREADSHEET_ID
        id_part = link.split('key=')[1]
        return id_part.split('&')[0].split('#')[0]

    # Trả về chính link nếu không phù hợp với bất kỳ định dạng nào
    return link
