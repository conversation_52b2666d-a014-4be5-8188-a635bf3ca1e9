from flask import Blueprint, render_template, request, jsonify, session
import traceback

from .logic import BasketArrangementLogic

# Thông tin chương trình
NAME = "Basket Arrangement"
DESCRIPTION = "Sắp xếp ID thông minh vào Google Sheet với thuật toán v2.0"
ICON = "🛒"

# Tạo Blueprint
basket_bp = Blueprint('basket_arrangement', __name__,
                     template_folder='templates',
                     static_folder='static',
                     url_prefix='/basket_arrangement')

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(basket_bp)

@basket_bp.route('/')
def index():
    """Trang chính của Basket Arrangement"""
    return render_template('basket_arrangement.html')

@basket_bp.route('/load_spreadsheet', methods=['POST'])
def load_spreadsheet():
    """API để load Google Spreadsheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '').strip()

        if not sheet_url:
            return jsonify({
                'success': False,
                'error': 'URL Google Sheet không được để trống'
            })

        # Sử dụng logic thực để load sheet
        logic = BasketArrangementLogic()

        try:
            # Load spreadsheet thực
            result = logic.load_spreadsheet(sheet_url)

            # Lưu logic instance vào session để sử dụng sau
            session['basket_arrangement'] = {
                'sheet_id': result['sheet_id'],
                'sheet_names': result['sheet_names'],
                'total_sheets': result['total_sheets']
            }
            session.modified = True

            return jsonify(result)

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Không thể kết nối đến Google Sheets: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/process_arrangement', methods=['POST'])
def process_arrangement():
    """API để xử lý sắp xếp ID"""
    try:
        data = request.get_json()

        # Lấy thông tin từ request
        conditions = data.get('conditions', [])
        sheet_config = data.get('sheet_config', {})

        # Lấy thông tin sheet từ session
        if 'basket_arrangement' not in session:
            return jsonify({
                'success': False,
                'error': 'Chưa load Google Sheet. Vui lòng load sheet trước.'
            }), 400

        sheet_info = session['basket_arrangement']
        sheet_id = sheet_info.get('sheet_id')

        if not sheet_id:
            return jsonify({
                'success': False,
                'error': 'Không tìm thấy thông tin sheet trong session.'
            }), 400

        if not conditions:
            return jsonify({
                'success': False,
                'error': 'Cần có ít nhất một điều kiện sắp xếp'
            })

        try:
            # Khởi tạo logic xử lý
            logic = BasketArrangementLogic()

            # Load spreadsheet
            sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit"
            logic.load_spreadsheet(sheet_url)

            # Xử lý sắp xếp thực tế
            result = logic.process_arrangement(conditions, sheet_config)

            return jsonify(result)

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Lỗi khi xử lý sắp xếp: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500