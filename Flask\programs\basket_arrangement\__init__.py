from flask import Blueprint, render_template, request, jsonify, session
import traceback

from .logic import EnhancedBasketArrangementLogic

# Thông tin chương trình
NAME = "Basket Arrangement"
DESCRIPTION = "Sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu"
ICON = "🛒"

# Tạo Blueprint
basket_bp = Blueprint('basket_arrangement', __name__,
                     template_folder='templates',
                     static_folder='static',
                     url_prefix='/basket_arrangement')

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(basket_bp)

@basket_bp.route('/')
def index():
    """Trang chính của Basket Arrangement"""
    return render_template('programs/basket_arrangement.html')

@basket_bp.route('/api/load_sheet', methods=['POST'])
def load_sheet():
    """API để load Google Sheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '').strip()

        if not sheet_url:
            return jsonify({
                'success': False,
                'error': 'URL Google Sheet không được để trống'
            })

        # Sử dụng logic thực để load sheet
        from .logic import EnhancedBasketArrangementLogic

        logic = EnhancedBasketArrangementLogic()

        try:
            # Load spreadsheet thực
            result = logic.load_spreadsheet(sheet_url)

            # Lưu thông tin vào session
            session['basket_arrangement'] = {
                'sheet_id': result['sheet_id'],
                'sheet_names': result['sheet_names'],
                'spreadsheet_title': f"Spreadsheet ({result['sheet_id'][:8]}...)",
                'is_real_connection': True
            }
            session.modified = True

            return jsonify({
                'success': True,
                'data': {
                    'sheet_id': result['sheet_id'],
                    'sheet_names': result['sheet_names'],
                    'total_sheets': result['total_sheets'],
                    'spreadsheet_title': session['basket_arrangement']['spreadsheet_title']
                }
            })

        except Exception as e:
            # Trả về lỗi thay vì sử dụng mock data
            return jsonify({
                'success': False,
                'error': f'Không thể kết nối đến Google Sheets: {str(e)}',
                'details': 'Vui lòng kiểm tra lại URL và quyền truy cập Google Sheets.'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_time_slots', methods=['POST'])
def get_time_slots():
    """API để lấy danh sách khung giờ từ sheet"""
    try:
        data = request.get_json()
        sheet_name = data.get('sheet_name', 'Basket')
        first_col = data.get('first_col', 'B')
        num_columns = data.get('num_columns', 12)

        # Lấy thông tin sheet từ session
        if 'basket_arrangement' not in session:
            return jsonify({
                'success': False,
                'error': 'Chưa load Google Sheet. Vui lòng load sheet trước.'
            }), 400

        sheet_info = session['basket_arrangement']

        # Nếu có kết nối thực, sử dụng logic thực
        if sheet_info.get('is_real_connection', False):
            try:
                from .logic import EnhancedBasketArrangementLogic

                logic = EnhancedBasketArrangementLogic()
                # Load lại spreadsheet
                sheet_id = sheet_info.get('sheet_id')
                if sheet_id:
                    # Load spreadsheet
                    result = logic.load_spreadsheet(f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit")

                    # Lấy time slots thực
                    time_slots = logic.get_time_slots(sheet_name, first_col, num_columns)

                    return jsonify({
                        'success': True,
                        'data': {
                            'time_slots': time_slots,
                            'total_slots': len(time_slots)
                        }
                    })

            except Exception as e:
                # Fallback về mock data nếu lỗi
                pass

        # Fallback về mock data với nhiều time slots
        mock_time_slots = [
            '09:00-10:00', '10:00-11:00', '11:00-12:00', '12:00-13:00',
            '13:00-14:00', '14:00-15:00', '15:00-16:00', '16:00-17:00',
            '17:00-18:00', '18:00-19:00', '19:00-20:00', '20:00-21:00'
        ]

        return jsonify({
            'success': True,
            'data': {
                'time_slots': mock_time_slots,
                'total_slots': len(mock_time_slots)
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/process_arrangement', methods=['POST'])
def process_arrangement():
    """API để xử lý sắp xếp ID"""
    try:
        data = request.get_json()

        # Lấy thông tin từ request
        basket_sheet_name = data.get('basket_sheet_name', 'Basket')
        deal_sheet_name = data.get('deal_sheet_name', 'Deal list')
        conditions = data.get('conditions', [])
        first_col = data.get('first_col', 'A')
        num_columns = data.get('num_columns', 12)

        # Lấy thông tin sheet từ session
        if 'basket_arrangement' not in session:
            return jsonify({
                'success': False,
                'error': 'Chưa load Google Sheet. Vui lòng load sheet trước.'
            }), 400

        sheet_info = session['basket_arrangement']
        sheet_id = sheet_info.get('sheet_id')

        if not sheet_id:
            return jsonify({
                'success': False,
                'error': 'Không tìm thấy thông tin sheet trong session.'
            }), 400

        if not conditions:
            return jsonify({
                'success': False,
                'error': 'Cần có ít nhất một điều kiện sắp xếp'
            })

        # Lấy column mapping từ session
        basket_session = session.get('basket_logic', {})
        column_mapping = basket_session.get('column_mapping', {
            'shop_id': 'A',
            'item_id': 'B',
            'gmv': 'C',
            'review': 'D',
            'no': 'E'
        })

        try:
            # Khởi tạo logic xử lý với credentials thực
            from .logic import EnhancedBasketArrangementLogic

            logic = EnhancedBasketArrangementLogic()

            # Load spreadsheet
            sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit"
            result = logic.load_spreadsheet(sheet_url)

            # Cấu hình sheet
            sheet_config = {
                'basket_sheet_name': basket_sheet_name,
                'deal_sheet_name': deal_sheet_name,
                'column_mapping': column_mapping,
                'first_col': first_col,
                'num_columns': num_columns
            }

            # Xử lý sắp xếp thực tế
            result = logic.process_arrangement(conditions, sheet_config)

            return jsonify({
                'success': True,
                'result': result
            })

        except Exception as e:
            # Trả về lỗi thay vì mock data
            return jsonify({
                'success': False,
                'error': f'Lỗi khi xử lý sắp xếp: {str(e)}',
                'details': 'Vui lòng kiểm tra lại dữ liệu và thử lại.',
                'traceback': traceback.format_exc()
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_column_mapping', methods=['GET'])
def get_column_mapping():
    """API để lấy thông tin mapping cột"""
    try:
        # Lấy mapping từ session hoặc sử dụng default
        mapping = session.get('basket_logic', {}).get('column_mapping', {
            'shop_id': 'A',
            'item_id': 'B',
            'gmv': 'C',
            'review': 'D',
            'no': 'E'
        })

        return jsonify({
            'success': True,
            'mapping': mapping
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/update_column_mapping', methods=['POST'])
def update_column_mapping():
    """API để cập nhật mapping cột"""
    try:
        data = request.get_json()
        mapping = data.get('mapping', {})

        # Lưu mapping vào session
        if 'basket_logic' not in session:
            session['basket_logic'] = {}
        session['basket_logic']['column_mapping'] = mapping
        session.modified = True

        return jsonify({
            'success': True,
            'message': 'Đã cập nhật mapping cột thành công'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
